{"artifacts": [{"path": "<PERSON>ster<PERSON>"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 10, "parent": 0}, {"command": 1, "file": 0, "line": 11, "parent": 0}, {"command": 2, "file": 0, "line": 8, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++17"}], "includes": [{"backtrace": 3, "path": "/usr/games/Homework3/Assignment3/./include"}, {"backtrace": 2, "isSystem": true, "path": "/usr/local/include/opencv4"}], "language": "CXX", "languageStandard": {"backtraces": [2], "standard": "17"}, "sourceIndexes": [0, 2, 5, 7]}], "id": "Rasterizer::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"fragment": "-Wl,-rpath,/usr/local/lib", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/lib/libopencv_gapi.so.4.12.0", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/lib/libopencv_highgui.so.4.12.0", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/lib/libopencv_ml.so.4.12.0", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/lib/libopencv_objdetect.so.4.12.0", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/lib/libopencv_photo.so.4.12.0", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/lib/libopencv_stitching.so.4.12.0", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/lib/libopencv_video.so.4.12.0", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/lib/libopencv_videoio.so.4.12.0", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/lib/libopencv_imgcodecs.so.4.12.0", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/lib/libopencv_dnn.so.4.12.0", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/lib/libopencv_calib3d.so.4.12.0", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/lib/libopencv_features2d.so.4.12.0", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/lib/libopencv_flann.so.4.12.0", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/lib/libopencv_imgproc.so.4.12.0", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/local/lib/libopencv_core.so.4.12.0", "role": "libraries"}], "language": "CXX"}, "name": "<PERSON>ster<PERSON>", "nameOnDisk": "<PERSON>ster<PERSON>", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 5, 7]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 3, 4, 6, 8, 9]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "rasterizer.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "rasterizer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "global.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Triangle.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Triangle.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Texture.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Texture.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Shader.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "OBJ_Loader.h", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}
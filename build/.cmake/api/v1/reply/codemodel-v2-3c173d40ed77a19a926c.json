{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "<PERSON>ster<PERSON>", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "Rasterizer::@6890427a1f51a3e7e1df", "jsonFile": "target-Rasterizer-Debug-ceafb21b9a59fb841479.json", "name": "<PERSON>ster<PERSON>", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/usr/games/Homework3/Assignment3/build", "source": "/usr/games/Homework3/Assignment3"}, "version": {"major": 2, "minor": 6}}
[{"directory": "/usr/games/Homework3/Assignment3/build", "command": "/usr/bin/g++  -I/usr/games/Homework3/Assignment3/./include -isystem /usr/local/include/opencv4 -g -std=gnu++17 -o CMakeFiles/Rasterizer.dir/main.cpp.o -c /usr/games/Homework3/Assignment3/main.cpp", "file": "/usr/games/Homework3/Assignment3/main.cpp", "output": "CMakeFiles/Rasterizer.dir/main.cpp.o"}, {"directory": "/usr/games/Homework3/Assignment3/build", "command": "/usr/bin/g++  -I/usr/games/Homework3/Assignment3/./include -isystem /usr/local/include/opencv4 -g -std=gnu++17 -o CMakeFiles/Rasterizer.dir/rasterizer.cpp.o -c /usr/games/Homework3/Assignment3/rasterizer.cpp", "file": "/usr/games/Homework3/Assignment3/rasterizer.cpp", "output": "CMakeFiles/Rasterizer.dir/rasterizer.cpp.o"}, {"directory": "/usr/games/Homework3/Assignment3/build", "command": "/usr/bin/g++  -I/usr/games/Homework3/Assignment3/./include -isystem /usr/local/include/opencv4 -g -std=gnu++17 -o CMakeFiles/Rasterizer.dir/Triangle.cpp.o -c /usr/games/Homework3/Assignment3/Triangle.cpp", "file": "/usr/games/Homework3/Assignment3/Triangle.cpp", "output": "CMakeFiles/Rasterizer.dir/Triangle.cpp.o"}, {"directory": "/usr/games/Homework3/Assignment3/build", "command": "/usr/bin/g++  -I/usr/games/Homework3/Assignment3/./include -isystem /usr/local/include/opencv4 -g -std=gnu++17 -o CMakeFiles/Rasterizer.dir/Texture.cpp.o -c /usr/games/Homework3/Assignment3/Texture.cpp", "file": "/usr/games/Homework3/Assignment3/Texture.cpp", "output": "CMakeFiles/Rasterizer.dir/Texture.cpp.o"}]